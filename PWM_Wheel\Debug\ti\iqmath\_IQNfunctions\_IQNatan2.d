# FIXED

ti/iqmath/_IQNfunctions/_IQNatan2.o: \
 ../ti/iqmath/_IQNfunctions/_IQNatan2.c \
 ../ti/iqmath/support/support.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/msp.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/DeviceFamily.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/m0p/mspm0g350x.h \
 D:/App_anzhuangbao/TI/CCS_20.2.0.00012/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include/core_cm0plus.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_adc12.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_aes.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_comp.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_crc.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_dac12.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_dma.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_flashctl.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_gpio.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_gptimer.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_i2c.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_iomux.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_mathacl.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_mcan.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_oa.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_rtc.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_spi.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_trng.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_uart.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_vref.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_wuc.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_wwdt.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/m0p/hw_factoryregion.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/m0p/hw_cpuss.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/m0p/hw_debugss.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/m0p/hw_sysctl.h \
 F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/m0p/sysctl/hw_sysctl_mspm0g1x0x_g3x0x.h \
 ../ti/iqmath/support/RTS_support.h \
 ../ti/iqmath/_IQNfunctions/_IQNtables.h \
 ../ti/iqmath/include/IQmathLib.h \
 ../ti/iqmath/_IQNfunctions/_IQNmpy.h \
 ../ti/iqmath/_IQNfunctions/_IQNdiv.h
../ti/iqmath/support/support.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/msp.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/DeviceFamily.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/m0p/mspm0g350x.h:
D:/App_anzhuangbao/TI/CCS_20.2.0.00012/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include/core_cm0plus.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_adc12.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_aes.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_comp.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_crc.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_dac12.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_dma.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_flashctl.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_gpio.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_gptimer.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_i2c.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_iomux.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_mathacl.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_mcan.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_oa.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_rtc.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_spi.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_trng.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_uart.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_vref.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_wuc.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/hw_wwdt.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/m0p/hw_factoryregion.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/m0p/hw_cpuss.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/m0p/hw_debugss.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/m0p/hw_sysctl.h:
F:/TI/CCS_Project/PWM_Wheel/ti/devices/msp/peripherals/m0p/sysctl/hw_sysctl_mspm0g1x0x_g3x0x.h:
../ti/iqmath/support/RTS_support.h:
../ti/iqmath/_IQNfunctions/_IQNtables.h:
../ti/iqmath/include/IQmathLib.h:
../ti/iqmath/_IQNfunctions/_IQNmpy.h:
../ti/iqmath/_IQNfunctions/_IQNdiv.h:
