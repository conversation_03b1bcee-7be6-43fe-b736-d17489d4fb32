################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/abnormalSpeedStallDetectConfig.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/closeLoopConfig.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/currentControlConfig.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/fluxWeakConfig.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/focConfig.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/hallEstimConfig.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/mtpaConfig.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/noMotorStallDetectConfig.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/openLoopConfig.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/stallDetectConfig.c 

C_DEPS += \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/abnormalSpeedStallDetectConfig.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/closeLoopConfig.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/currentControlConfig.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/fluxWeakConfig.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/focConfig.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/hallEstimConfig.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/mtpaConfig.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/noMotorStallDetectConfig.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/openLoopConfig.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/stallDetectConfig.d 

OBJS += \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/abnormalSpeedStallDetectConfig.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/closeLoopConfig.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/currentControlConfig.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/fluxWeakConfig.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/focConfig.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/hallEstimConfig.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/mtpaConfig.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/noMotorStallDetectConfig.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/openLoopConfig.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/stallDetectConfig.o 

OBJS__QUOTED += \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\abnormalSpeedStallDetectConfig.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\closeLoopConfig.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\currentControlConfig.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\fluxWeakConfig.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\focConfig.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\hallEstimConfig.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\mtpaConfig.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\noMotorStallDetectConfig.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\openLoopConfig.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\stallDetectConfig.o" 

C_DEPS__QUOTED += \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\abnormalSpeedStallDetectConfig.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\closeLoopConfig.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\currentControlConfig.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\fluxWeakConfig.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\focConfig.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\hallEstimConfig.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\mtpaConfig.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\noMotorStallDetectConfig.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\openLoopConfig.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\modulesInterconnect\algoConfig\focConfig\source\stallDetectConfig.d" 

C_SRCS__QUOTED += \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/abnormalSpeedStallDetectConfig.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/closeLoopConfig.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/currentControlConfig.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/fluxWeakConfig.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/focConfig.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/hallEstimConfig.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/mtpaConfig.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/noMotorStallDetectConfig.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/openLoopConfig.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/modulesInterconnect/algoConfig/focConfig/source/stallDetectConfig.c" 


