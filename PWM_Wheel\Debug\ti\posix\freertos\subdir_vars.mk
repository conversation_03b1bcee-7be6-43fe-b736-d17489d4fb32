################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../ti/posix/freertos/Mtx.c \
../ti/posix/freertos/PTLS.c \
../ti/posix/freertos/aeabi_portable.c \
../ti/posix/freertos/clock.c \
../ti/posix/freertos/memory.c \
../ti/posix/freertos/mqueue.c \
../ti/posix/freertos/pthread.c \
../ti/posix/freertos/pthread_barrier.c \
../ti/posix/freertos/pthread_cond.c \
../ti/posix/freertos/pthread_mutex.c \
../ti/posix/freertos/pthread_rwlock.c \
../ti/posix/freertos/reent.c \
../ti/posix/freertos/sched.c \
../ti/posix/freertos/semaphore.c \
../ti/posix/freertos/sleep.c \
../ti/posix/freertos/timer.c 

C_DEPS += \
./ti/posix/freertos/Mtx.d \
./ti/posix/freertos/PTLS.d \
./ti/posix/freertos/aeabi_portable.d \
./ti/posix/freertos/clock.d \
./ti/posix/freertos/memory.d \
./ti/posix/freertos/mqueue.d \
./ti/posix/freertos/pthread.d \
./ti/posix/freertos/pthread_barrier.d \
./ti/posix/freertos/pthread_cond.d \
./ti/posix/freertos/pthread_mutex.d \
./ti/posix/freertos/pthread_rwlock.d \
./ti/posix/freertos/reent.d \
./ti/posix/freertos/sched.d \
./ti/posix/freertos/semaphore.d \
./ti/posix/freertos/sleep.d \
./ti/posix/freertos/timer.d 

OBJS += \
./ti/posix/freertos/Mtx.o \
./ti/posix/freertos/PTLS.o \
./ti/posix/freertos/aeabi_portable.o \
./ti/posix/freertos/clock.o \
./ti/posix/freertos/memory.o \
./ti/posix/freertos/mqueue.o \
./ti/posix/freertos/pthread.o \
./ti/posix/freertos/pthread_barrier.o \
./ti/posix/freertos/pthread_cond.o \
./ti/posix/freertos/pthread_mutex.o \
./ti/posix/freertos/pthread_rwlock.o \
./ti/posix/freertos/reent.o \
./ti/posix/freertos/sched.o \
./ti/posix/freertos/semaphore.o \
./ti/posix/freertos/sleep.o \
./ti/posix/freertos/timer.o 

OBJS__QUOTED += \
"ti\posix\freertos\Mtx.o" \
"ti\posix\freertos\PTLS.o" \
"ti\posix\freertos\aeabi_portable.o" \
"ti\posix\freertos\clock.o" \
"ti\posix\freertos\memory.o" \
"ti\posix\freertos\mqueue.o" \
"ti\posix\freertos\pthread.o" \
"ti\posix\freertos\pthread_barrier.o" \
"ti\posix\freertos\pthread_cond.o" \
"ti\posix\freertos\pthread_mutex.o" \
"ti\posix\freertos\pthread_rwlock.o" \
"ti\posix\freertos\reent.o" \
"ti\posix\freertos\sched.o" \
"ti\posix\freertos\semaphore.o" \
"ti\posix\freertos\sleep.o" \
"ti\posix\freertos\timer.o" 

C_DEPS__QUOTED += \
"ti\posix\freertos\Mtx.d" \
"ti\posix\freertos\PTLS.d" \
"ti\posix\freertos\aeabi_portable.d" \
"ti\posix\freertos\clock.d" \
"ti\posix\freertos\memory.d" \
"ti\posix\freertos\mqueue.d" \
"ti\posix\freertos\pthread.d" \
"ti\posix\freertos\pthread_barrier.d" \
"ti\posix\freertos\pthread_cond.d" \
"ti\posix\freertos\pthread_mutex.d" \
"ti\posix\freertos\pthread_rwlock.d" \
"ti\posix\freertos\reent.d" \
"ti\posix\freertos\sched.d" \
"ti\posix\freertos\semaphore.d" \
"ti\posix\freertos\sleep.d" \
"ti\posix\freertos\timer.d" 

C_SRCS__QUOTED += \
"../ti/posix/freertos/Mtx.c" \
"../ti/posix/freertos/PTLS.c" \
"../ti/posix/freertos/aeabi_portable.c" \
"../ti/posix/freertos/clock.c" \
"../ti/posix/freertos/memory.c" \
"../ti/posix/freertos/mqueue.c" \
"../ti/posix/freertos/pthread.c" \
"../ti/posix/freertos/pthread_barrier.c" \
"../ti/posix/freertos/pthread_cond.c" \
"../ti/posix/freertos/pthread_mutex.c" \
"../ti/posix/freertos/pthread_rwlock.c" \
"../ti/posix/freertos/reent.c" \
"../ti/posix/freertos/sched.c" \
"../ti/posix/freertos/semaphore.c" \
"../ti/posix/freertos/sleep.c" \
"../ti/posix/freertos/timer.c" 


