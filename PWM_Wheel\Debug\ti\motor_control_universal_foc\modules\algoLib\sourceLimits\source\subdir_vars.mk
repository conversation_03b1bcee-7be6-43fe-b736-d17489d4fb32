################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/overCurrent.c \
../ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceCurrentEstim.c \
../ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceCurrentLimit.c \
../ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceLimits.c \
../ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceVoltageLimit.c 

C_DEPS += \
./ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/overCurrent.d \
./ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceCurrentEstim.d \
./ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceCurrentLimit.d \
./ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceLimits.d \
./ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceVoltageLimit.d 

OBJS += \
./ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/overCurrent.o \
./ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceCurrentEstim.o \
./ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceCurrentLimit.o \
./ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceLimits.o \
./ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceVoltageLimit.o 

OBJS__QUOTED += \
"ti\motor_control_universal_foc\modules\algoLib\sourceLimits\source\overCurrent.o" \
"ti\motor_control_universal_foc\modules\algoLib\sourceLimits\source\sourceCurrentEstim.o" \
"ti\motor_control_universal_foc\modules\algoLib\sourceLimits\source\sourceCurrentLimit.o" \
"ti\motor_control_universal_foc\modules\algoLib\sourceLimits\source\sourceLimits.o" \
"ti\motor_control_universal_foc\modules\algoLib\sourceLimits\source\sourceVoltageLimit.o" 

C_DEPS__QUOTED += \
"ti\motor_control_universal_foc\modules\algoLib\sourceLimits\source\overCurrent.d" \
"ti\motor_control_universal_foc\modules\algoLib\sourceLimits\source\sourceCurrentEstim.d" \
"ti\motor_control_universal_foc\modules\algoLib\sourceLimits\source\sourceCurrentLimit.d" \
"ti\motor_control_universal_foc\modules\algoLib\sourceLimits\source\sourceLimits.d" \
"ti\motor_control_universal_foc\modules\algoLib\sourceLimits\source\sourceVoltageLimit.d" 

C_SRCS__QUOTED += \
"../ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/overCurrent.c" \
"../ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceCurrentEstim.c" \
"../ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceCurrentLimit.c" \
"../ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceLimits.c" \
"../ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/sourceVoltageLimit.c" 


