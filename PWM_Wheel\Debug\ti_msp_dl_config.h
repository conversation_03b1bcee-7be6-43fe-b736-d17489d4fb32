/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define CPUCLK_FREQ                                                     32000000



/* Defines for PWM_Wheel */
#define PWM_Wheel_INST                                                    TIMG12
#define PWM_Wheel_INST_IRQHandler                              TIMG12_IRQHandler
#define PWM_Wheel_INST_INT_IRQN                                (TIMG12_INT_IRQn)
#define PWM_Wheel_INST_CLK_FREQ                                          4000000
/* GPIO defines for channel 1 */
#define GPIO_PWM_Wheel_C1_PORT                                             GPIOA
#define GPIO_PWM_Wheel_C1_PIN                                     DL_GPIO_PIN_25
#define GPIO_PWM_Wheel_C1_IOMUX                                  (IOMUX_PINCM55)
#define GPIO_PWM_Wheel_C1_IOMUX_FUNC                IOMUX_PINCM55_PF_TIMG12_CCP1
#define GPIO_PWM_Wheel_C1_IDX                                DL_TIMER_CC_1_INDEX



/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                           32000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                (115200)
#define UART_0_IBRD_32_MHZ_115200_BAUD                                      (17)
#define UART_0_FBRD_32_MHZ_115200_BAUD                                      (23)





/* Port definition for Pin Group KEY */
#define KEY_PORT                                                         (GPIOB)

/* Defines for PIN_21: GPIOB.21 with pinCMx 49 on package pin 20 */
// pins affected by this interrupt request:["PIN_21"]
#define KEY_INT_IRQN                                            (GPIOB_INT_IRQn)
#define KEY_INT_IIDX                            (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define KEY_PIN_21_IIDX                                     (DL_GPIO_IIDX_DIO21)
#define KEY_PIN_21_PIN                                          (DL_GPIO_PIN_21)
#define KEY_PIN_21_IOMUX                                         (IOMUX_PINCM49)
/* Port definition for Pin Group R1 */
#define R1_PORT                                                          (GPIOA)

/* Defines for PIN_26: GPIOA.26 with pinCMx 59 on package pin 30 */
// groups represented: ["R2","M","L1","L2","R1"]
// pins affected: ["PIN_24","PIN_22","PIN_15","PIN_17","PIN_26"]
#define GPIO_MULTIPLE_GPIOA_INT_IRQN                            (GPIOA_INT_IRQn)
#define GPIO_MULTIPLE_GPIOA_INT_IIDX            (DL_INTERRUPT_GROUP1_IIDX_GPIOA)
#define R1_PIN_26_IIDX                                      (DL_GPIO_IIDX_DIO26)
#define R1_PIN_26_PIN                                           (DL_GPIO_PIN_26)
#define R1_PIN_26_IOMUX                                          (IOMUX_PINCM59)
/* Port definition for Pin Group R2 */
#define R2_PORT                                                          (GPIOA)

/* Defines for PIN_24: GPIOA.24 with pinCMx 54 on package pin 25 */
#define R2_PIN_24_IIDX                                      (DL_GPIO_IIDX_DIO24)
#define R2_PIN_24_PIN                                           (DL_GPIO_PIN_24)
#define R2_PIN_24_IOMUX                                          (IOMUX_PINCM54)
/* Port definition for Pin Group M */
#define M_PORT                                                           (GPIOA)

/* Defines for PIN_22: GPIOA.22 with pinCMx 47 on package pin 18 */
#define M_PIN_22_IIDX                                       (DL_GPIO_IIDX_DIO22)
#define M_PIN_22_PIN                                            (DL_GPIO_PIN_22)
#define M_PIN_22_IOMUX                                           (IOMUX_PINCM47)
/* Port definition for Pin Group L1 */
#define L1_PORT                                                          (GPIOA)

/* Defines for PIN_15: GPIOA.15 with pinCMx 37 on package pin 8 */
#define L1_PIN_15_IIDX                                      (DL_GPIO_IIDX_DIO15)
#define L1_PIN_15_PIN                                           (DL_GPIO_PIN_15)
#define L1_PIN_15_IOMUX                                          (IOMUX_PINCM37)
/* Port definition for Pin Group L2 */
#define L2_PORT                                                          (GPIOA)

/* Defines for PIN_17: GPIOA.17 with pinCMx 39 on package pin 10 */
#define L2_PIN_17_IIDX                                      (DL_GPIO_IIDX_DIO17)
#define L2_PIN_17_PIN                                           (DL_GPIO_PIN_17)
#define L2_PIN_17_IOMUX                                          (IOMUX_PINCM39)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_Wheel_init(void);
void SYSCFG_DL_UART_0_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
