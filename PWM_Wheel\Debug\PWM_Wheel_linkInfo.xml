<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o PWM_Wheel.out -mPWM_Wheel.map -iD:/App_anzhuangbao/TI/CCS_20.2.0.00012/mspm0_sdk_2_05_01_00/source -iF:/TI/CCS_Project/PWM_Wheel -iF:/TI/CCS_Project/PWM_Wheel/Debug/syscfg -iD:/App_anzhuangbao/TI/CCS_20.2.0.00012/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=PWM_Wheel_linkInfo.xml --rom_model ./empty.o -l./device_linker.cmd ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x688bb4bd</link_time>
   <link_errors>0x0</link_errors>
   <output_file>F:\TI\CCS_Project\PWM_Wheel\Debug\PWM_Wheel.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x5ed</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>F:\TI\CCS_Project\PWM_Wheel\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>F:\TI\CCS_Project\PWM_Wheel\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>F:\TI\CCS_Project\PWM_Wheel\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>F:\TI\CCS_Project\PWM_Wheel\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-11">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-2a">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d4">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-84">
         <name>.text:memcpy</name>
         <load_address>0x1c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c4</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x25e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x260</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x260</run_address>
         <size>0x94</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-af">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x2f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2f4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x384</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x384</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x400</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x400</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.SYSCFG_DL_PWM_Wheel_init</name>
         <load_address>0x474</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x474</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.DL_UART_init</name>
         <load_address>0x4e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4e8</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x530</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x530</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x570</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x570</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x5b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5b0</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x5ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5ec</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-72">
         <name>.text.main</name>
         <load_address>0x614</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x614</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x638</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x638</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x654</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x654</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x670</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x670</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-89">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x688</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x688</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x6a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a0</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x6b2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b2</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x6c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x6d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6d4</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-44">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x6e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.text:abort</name>
         <load_address>0x6e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e8</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.HOSTexit</name>
         <load_address>0x6ee</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6ee</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x6f2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text._system_pre_init</name>
         <load_address>0x6f6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.cinit..data.load</name>
         <load_address>0x718</load_address>
         <readonly>true</readonly>
         <run_address>0x718</run_address>
         <size>0xd</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-129">
         <name>__TI_handler_table</name>
         <load_address>0x728</load_address>
         <readonly>true</readonly>
         <run_address>0x728</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-12a">
         <name>__TI_cinit_table</name>
         <load_address>0x730</load_address>
         <readonly>true</readonly>
         <run_address>0x730</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-d9">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x700</load_address>
         <readonly>true</readonly>
         <run_address>0x700</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x70a</load_address>
         <readonly>true</readonly>
         <run_address>0x70a</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.rodata.gPWM_WheelConfig</name>
         <load_address>0x70c</load_address>
         <readonly>true</readonly>
         <run_address>0x70c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.rodata.gPWM_WheelClockConfig</name>
         <load_address>0x714</load_address>
         <readonly>true</readonly>
         <run_address>0x714</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-62">
         <name>.data.pwm_duty_cycle</name>
         <load_address>0x20200004</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200004</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-61">
         <name>.data.pwm_direction</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x19a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_abbrev</name>
         <load_address>0x19a</load_address>
         <run_address>0x19a</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_abbrev</name>
         <load_address>0x35b</load_address>
         <run_address>0x35b</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_abbrev</name>
         <load_address>0x3c8</load_address>
         <run_address>0x3c8</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_abbrev</name>
         <load_address>0x42a</load_address>
         <run_address>0x42a</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_abbrev</name>
         <load_address>0x6b0</load_address>
         <run_address>0x6b0</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0x94b</load_address>
         <run_address>0x94b</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_abbrev</name>
         <load_address>0x9fa</load_address>
         <run_address>0x9fa</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_abbrev</name>
         <load_address>0xb6a</load_address>
         <run_address>0xb6a</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_abbrev</name>
         <load_address>0xba3</load_address>
         <run_address>0xba3</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0xc13</load_address>
         <run_address>0xc13</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_abbrev</name>
         <load_address>0xca0</load_address>
         <run_address>0xca0</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_abbrev</name>
         <load_address>0xd38</load_address>
         <run_address>0xd38</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_abbrev</name>
         <load_address>0xd64</load_address>
         <run_address>0xd64</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_abbrev</name>
         <load_address>0xd8b</load_address>
         <run_address>0xd8b</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_abbrev</name>
         <load_address>0xdb0</load_address>
         <run_address>0xdb0</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1111</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_info</name>
         <load_address>0x1111</load_address>
         <run_address>0x1111</run_address>
         <size>0x2a72</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x3b83</load_address>
         <run_address>0x3b83</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_info</name>
         <load_address>0x3c03</load_address>
         <run_address>0x3c03</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_info</name>
         <load_address>0x3c78</load_address>
         <run_address>0x3c78</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_info</name>
         <load_address>0x6dea</load_address>
         <run_address>0x6dea</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x8090</load_address>
         <run_address>0x8090</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_info</name>
         <load_address>0x84b3</load_address>
         <run_address>0x84b3</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_info</name>
         <load_address>0x8bf7</load_address>
         <run_address>0x8bf7</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_info</name>
         <load_address>0x8c3d</load_address>
         <run_address>0x8c3d</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0x8d03</load_address>
         <run_address>0x8d03</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_info</name>
         <load_address>0x8e7f</load_address>
         <run_address>0x8e7f</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_info</name>
         <load_address>0x8f77</load_address>
         <run_address>0x8f77</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_info</name>
         <load_address>0x8fb2</load_address>
         <run_address>0x8fb2</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_info</name>
         <load_address>0x914b</load_address>
         <run_address>0x914b</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_info</name>
         <load_address>0x9445</load_address>
         <run_address>0x9445</run_address>
         <size>0x95</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_ranges</name>
         <load_address>0x18</load_address>
         <run_address>0x18</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_ranges</name>
         <load_address>0x50</load_address>
         <run_address>0x50</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_ranges</name>
         <load_address>0x68</load_address>
         <run_address>0x68</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_ranges</name>
         <load_address>0x240</load_address>
         <run_address>0x240</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x3e8</load_address>
         <run_address>0x3e8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_ranges</name>
         <load_address>0x430</load_address>
         <run_address>0x430</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x478</load_address>
         <run_address>0x478</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_ranges</name>
         <load_address>0x4c8</load_address>
         <run_address>0x4c8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_ranges</name>
         <load_address>0x4e0</load_address>
         <run_address>0x4e0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9cb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_str</name>
         <load_address>0x9cb</load_address>
         <run_address>0x9cb</run_address>
         <size>0x224b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_str</name>
         <load_address>0x2c16</load_address>
         <run_address>0x2c16</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_str</name>
         <load_address>0x2d83</load_address>
         <run_address>0x2d83</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_str</name>
         <load_address>0x2ef0</load_address>
         <run_address>0x2ef0</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_str</name>
         <load_address>0x4cbc</load_address>
         <run_address>0x4cbc</run_address>
         <size>0xce3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x599f</load_address>
         <run_address>0x599f</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_str</name>
         <load_address>0x5bc4</load_address>
         <run_address>0x5bc4</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_str</name>
         <load_address>0x5ef3</load_address>
         <run_address>0x5ef3</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_str</name>
         <load_address>0x5fe8</load_address>
         <run_address>0x5fe8</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0x6150</load_address>
         <run_address>0x6150</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_str</name>
         <load_address>0x6325</load_address>
         <run_address>0x6325</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_str</name>
         <load_address>0x646d</load_address>
         <run_address>0x646d</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_frame</name>
         <load_address>0x38</load_address>
         <run_address>0x38</run_address>
         <size>0x98</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0xd0</load_address>
         <run_address>0xd0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_frame</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_frame</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_frame</name>
         <load_address>0x528</load_address>
         <run_address>0x528</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_frame</name>
         <load_address>0x6e0</load_address>
         <run_address>0x6e0</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_frame</name>
         <load_address>0x770</load_address>
         <run_address>0x770</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_frame</name>
         <load_address>0x870</load_address>
         <run_address>0x870</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0x890</load_address>
         <run_address>0x890</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0x8b8</load_address>
         <run_address>0x8b8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_frame</name>
         <load_address>0x8e8</load_address>
         <run_address>0x8e8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_frame</name>
         <load_address>0x918</load_address>
         <run_address>0x918</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x457</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_line</name>
         <load_address>0x457</load_address>
         <run_address>0x457</run_address>
         <size>0x667</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0xabe</load_address>
         <run_address>0xabe</run_address>
         <size>0xd9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_line</name>
         <load_address>0xb97</load_address>
         <run_address>0xb97</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_line</name>
         <load_address>0xd0f</load_address>
         <run_address>0xd0f</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_line</name>
         <load_address>0x247d</load_address>
         <run_address>0x247d</run_address>
         <size>0xa17</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x2e94</load_address>
         <run_address>0x2e94</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_line</name>
         <load_address>0x3070</load_address>
         <run_address>0x3070</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_line</name>
         <load_address>0x358a</load_address>
         <run_address>0x358a</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x35c8</load_address>
         <run_address>0x35c8</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x3688</load_address>
         <run_address>0x3688</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_line</name>
         <load_address>0x3850</load_address>
         <run_address>0x3850</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_line</name>
         <load_address>0x38b7</load_address>
         <run_address>0x38b7</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_line</name>
         <load_address>0x38f8</load_address>
         <run_address>0x38f8</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_line</name>
         <load_address>0x399c</load_address>
         <run_address>0x399c</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x12f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_loc</name>
         <load_address>0x12f</load_address>
         <run_address>0x12f</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_loc</name>
         <load_address>0x142</load_address>
         <run_address>0x142</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_loc</name>
         <load_address>0x1b69</load_address>
         <run_address>0x1b69</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_loc</name>
         <load_address>0x2325</load_address>
         <run_address>0x2325</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_loc</name>
         <load_address>0x23fd</load_address>
         <run_address>0x23fd</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_loc</name>
         <load_address>0x2821</load_address>
         <run_address>0x2821</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_loc</name>
         <load_address>0x2890</load_address>
         <run_address>0x2890</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_loc</name>
         <load_address>0x29f7</load_address>
         <run_address>0x29f7</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x640</size>
         <contents>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x718</load_address>
         <run_address>0x718</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-12a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x700</load_address>
         <run_address>0x700</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-cf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-f3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-61"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-12d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ea" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-eb" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ec" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ed" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ee" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ef" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f1" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10d" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xdbf</size>
         <contents>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-12f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10f" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x94da</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-12e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-111" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x508</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-81"/>
         </contents>
      </logical_group>
      <logical_group id="lg-113" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6556</size>
         <contents>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-db"/>
         </contents>
      </logical_group>
      <logical_group id="lg-115" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x938</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-b9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-117" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3a3c</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-119" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2a1d</size>
         <contents>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-94"/>
         </contents>
      </logical_group>
      <logical_group id="lg-123" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-80"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12c" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-134" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x738</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-135" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x8</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-136" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x738</used_space>
         <unused_space>0x1f8c8</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x640</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x700</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x718</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x738</start_address>
               <size>0x1f8c8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x208</used_space>
         <unused_space>0x7df8</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-ef"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-f1"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200008</start_address>
               <size>0x7df8</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x718</load_address>
            <load_size>0xd</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x8</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x730</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x738</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x738</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x728</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x730</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3f">
         <name>GROUP1_IRQHandler</name>
         <value>0x401</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-40">
         <name>pwm_direction</name>
         <value>0x20200000</value>
         <object_component_ref idref="oc-61"/>
      </symbol>
      <symbol id="sm-41">
         <name>pwm_duty_cycle</name>
         <value>0x20200004</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-42">
         <name>main</name>
         <value>0x615</value>
         <object_component_ref idref="oc-72"/>
      </symbol>
      <symbol id="sm-63">
         <name>SYSCFG_DL_init</name>
         <value>0x689</value>
         <object_component_ref idref="oc-89"/>
      </symbol>
      <symbol id="sm-64">
         <name>SYSCFG_DL_initPower</name>
         <value>0x571</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-65">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x2f5</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-66">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x531</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-67">
         <name>SYSCFG_DL_PWM_Wheel_init</name>
         <value>0x475</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-68">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x261</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-73">
         <name>Default_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>Reset_Handler</name>
         <value>0x6f3</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-75">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-76">
         <name>NMI_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>HardFault_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>SVC_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>PendSV_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>SysTick_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>GROUP0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>TIMG8_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>UART3_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>ADC0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>ADC1_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>CANFD0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>DAC0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>SPI0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>SPI1_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>UART1_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-85">
         <name>UART2_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>UART0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>TIMG0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>TIMG6_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>TIMA0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>TIMA1_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>TIMG7_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>TIMG12_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>I2C0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>I2C1_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>AES_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>RTC_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>DMA_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-93">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-94">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-95">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-96">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-97">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-98">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-99">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-9a">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a3">
         <name>DL_Common_delayCycles</name>
         <value>0x6d5</value>
         <object_component_ref idref="oc-c7"/>
      </symbol>
      <symbol id="sm-ba">
         <name>DL_Timer_setClockConfig</name>
         <value>0x655</value>
         <object_component_ref idref="oc-cb"/>
      </symbol>
      <symbol id="sm-bb">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x6c5</value>
         <object_component_ref idref="oc-5b"/>
      </symbol>
      <symbol id="sm-bc">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x639</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-bd">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x671</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-be">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-cc"/>
      </symbol>
      <symbol id="sm-cb">
         <name>DL_UART_init</name>
         <value>0x4e9</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-cc">
         <name>DL_UART_setClockConfig</name>
         <value>0x6a1</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-d7">
         <name>_c_int00_noargs</name>
         <value>0x5ed</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-d8">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-e4">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x5b1</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-ec">
         <name>_system_pre_init</name>
         <value>0x6f7</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-f7">
         <name>__TI_decompress_none</name>
         <value>0x6b3</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-102">
         <name>__TI_decompress_lzss</name>
         <value>0x385</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-10c">
         <name>abort</name>
         <value>0x6e9</value>
         <object_component_ref idref="oc-8e"/>
      </symbol>
      <symbol id="sm-116">
         <name>HOSTexit</name>
         <value>0x6ef</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-117">
         <name>C$$EXIT</name>
         <value>0x6ee</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-11d">
         <name>__aeabi_memcpy</name>
         <value>0x6e1</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-11e">
         <name>__aeabi_memcpy4</name>
         <value>0x6e1</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-11f">
         <name>__aeabi_memcpy8</name>
         <value>0x6e1</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-139">
         <name>memcpy</name>
         <value>0x1c5</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-13a">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-13d">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-13e">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
