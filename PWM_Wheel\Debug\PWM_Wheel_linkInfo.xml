<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o PWM_Wheel.out -mPWM_Wheel.map -iD:/App_anzhuangbao/TI/CCS_20.2.0.00012/mspm0_sdk_2_05_01_00/source -iF:/TI/CCS_Project/PWM_Wheel -iF:/TI/CCS_Project/PWM_Wheel/Debug/syscfg -iD:/App_anzhuangbao/TI/CCS_20.2.0.00012/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=PWM_Wheel_linkInfo.xml --rom_model ./empty.o -l./device_linker.cmd ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x688b8b1f</link_time>
   <link_errors>0x0</link_errors>
   <output_file>F:\TI\CCS_Project\PWM_Wheel\Debug\PWM_Wheel.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x4b9</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>F:\TI\CCS_Project\PWM_Wheel\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>F:\TI\CCS_Project\PWM_Wheel\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>F:\TI\CCS_Project\PWM_Wheel\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>F:\TI\CCS_Project\PWM_Wheel\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-11">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-29">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2a">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d3">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d4">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-84">
         <name>.text:memcpy</name>
         <load_address>0x1c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c4</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x25e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x260</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x260</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x2dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2dc</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.SYSCFG_DL_PWM_Wheel_init</name>
         <load_address>0x350</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x350</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-af">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x3c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3c4</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x408</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x408</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x448</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x448</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x484</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x484</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x4b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4b8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-72">
         <name>.text.main</name>
         <load_address>0x4e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4e0</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x504</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x504</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x520</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x520</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x53c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x53c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-89">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x554</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x554</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x568</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x568</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x57c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x57c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x58c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x58c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-44">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x598</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x598</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.text:abort</name>
         <load_address>0x5a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5a0</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.text.HOSTexit</name>
         <load_address>0x5a6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5a6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x5aa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5aa</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text._system_pre_init</name>
         <load_address>0x5ae</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5ae</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.cinit..data.load</name>
         <load_address>0x5c8</load_address>
         <readonly>true</readonly>
         <run_address>0x5c8</run_address>
         <size>0xd</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-11d">
         <name>__TI_handler_table</name>
         <load_address>0x5d8</load_address>
         <readonly>true</readonly>
         <run_address>0x5d8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-11e">
         <name>__TI_cinit_table</name>
         <load_address>0x5e0</load_address>
         <readonly>true</readonly>
         <run_address>0x5e0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-cf">
         <name>.rodata.gPWM_WheelConfig</name>
         <load_address>0x5b8</load_address>
         <readonly>true</readonly>
         <run_address>0x5b8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.rodata.gPWM_WheelClockConfig</name>
         <load_address>0x5c0</load_address>
         <readonly>true</readonly>
         <run_address>0x5c0</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-62">
         <name>.data.pwm_duty_cycle</name>
         <load_address>0x20200004</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200004</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-61">
         <name>.data.pwm_direction</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-121">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x19a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_abbrev</name>
         <load_address>0x19a</load_address>
         <run_address>0x19a</run_address>
         <size>0x1b1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_abbrev</name>
         <load_address>0x34b</load_address>
         <run_address>0x34b</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_abbrev</name>
         <load_address>0x3b8</load_address>
         <run_address>0x3b8</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_abbrev</name>
         <load_address>0x41a</load_address>
         <run_address>0x41a</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0x6a0</load_address>
         <run_address>0x6a0</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_abbrev</name>
         <load_address>0x74f</load_address>
         <run_address>0x74f</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_abbrev</name>
         <load_address>0x8bf</load_address>
         <run_address>0x8bf</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_abbrev</name>
         <load_address>0x8f8</load_address>
         <run_address>0x8f8</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0x968</load_address>
         <run_address>0x968</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_abbrev</name>
         <load_address>0x9f5</load_address>
         <run_address>0x9f5</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_abbrev</name>
         <load_address>0xa8d</load_address>
         <run_address>0xa8d</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_abbrev</name>
         <load_address>0xab9</load_address>
         <run_address>0xab9</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_abbrev</name>
         <load_address>0xae0</load_address>
         <run_address>0xae0</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_abbrev</name>
         <load_address>0xb05</load_address>
         <run_address>0xb05</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1111</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_info</name>
         <load_address>0x1111</load_address>
         <run_address>0x1111</run_address>
         <size>0x1d72</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x2e83</load_address>
         <run_address>0x2e83</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_info</name>
         <load_address>0x2f03</load_address>
         <run_address>0x2f03</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_info</name>
         <load_address>0x2f78</load_address>
         <run_address>0x2f78</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x60ea</load_address>
         <run_address>0x60ea</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_info</name>
         <load_address>0x650d</load_address>
         <run_address>0x650d</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_info</name>
         <load_address>0x6c51</load_address>
         <run_address>0x6c51</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_info</name>
         <load_address>0x6c97</load_address>
         <run_address>0x6c97</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0x6d5d</load_address>
         <run_address>0x6d5d</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_info</name>
         <load_address>0x6ed9</load_address>
         <run_address>0x6ed9</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_info</name>
         <load_address>0x6fd1</load_address>
         <run_address>0x6fd1</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_info</name>
         <load_address>0x700c</load_address>
         <run_address>0x700c</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_info</name>
         <load_address>0x71a5</load_address>
         <run_address>0x71a5</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-122">
         <name>.debug_info</name>
         <load_address>0x749f</load_address>
         <run_address>0x749f</run_address>
         <size>0x95</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_ranges</name>
         <load_address>0x18</load_address>
         <run_address>0x18</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_ranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x238</load_address>
         <run_address>0x238</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_ranges</name>
         <load_address>0x280</load_address>
         <run_address>0x280</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x2c8</load_address>
         <run_address>0x2c8</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_ranges</name>
         <load_address>0x318</load_address>
         <run_address>0x318</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_ranges</name>
         <load_address>0x330</load_address>
         <run_address>0x330</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9cb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_str</name>
         <load_address>0x9cb</load_address>
         <run_address>0x9cb</run_address>
         <size>0x1950</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_str</name>
         <load_address>0x231b</load_address>
         <run_address>0x231b</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_str</name>
         <load_address>0x2488</load_address>
         <run_address>0x2488</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_str</name>
         <load_address>0x25f5</load_address>
         <run_address>0x25f5</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x43c1</load_address>
         <run_address>0x43c1</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_str</name>
         <load_address>0x45e6</load_address>
         <run_address>0x45e6</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_str</name>
         <load_address>0x4915</load_address>
         <run_address>0x4915</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_str</name>
         <load_address>0x4a0a</load_address>
         <run_address>0x4a0a</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0x4b72</load_address>
         <run_address>0x4b72</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_str</name>
         <load_address>0x4d47</load_address>
         <run_address>0x4d47</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_str</name>
         <load_address>0x4e8f</load_address>
         <run_address>0x4e8f</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_frame</name>
         <load_address>0x38</load_address>
         <run_address>0x38</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0xb8</load_address>
         <run_address>0xb8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_frame</name>
         <load_address>0xe8</load_address>
         <run_address>0xe8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_frame</name>
         <load_address>0x108</load_address>
         <run_address>0x108</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_frame</name>
         <load_address>0x510</load_address>
         <run_address>0x510</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_frame</name>
         <load_address>0x5a0</load_address>
         <run_address>0x5a0</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_frame</name>
         <load_address>0x6a0</load_address>
         <run_address>0x6a0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x6c0</load_address>
         <run_address>0x6c0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0x6e8</load_address>
         <run_address>0x6e8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_frame</name>
         <load_address>0x718</load_address>
         <run_address>0x718</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_frame</name>
         <load_address>0x748</load_address>
         <run_address>0x748</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x44a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_line</name>
         <load_address>0x44a</load_address>
         <run_address>0x44a</run_address>
         <size>0x529</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x973</load_address>
         <run_address>0x973</run_address>
         <size>0xd9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_line</name>
         <load_address>0xa4c</load_address>
         <run_address>0xa4c</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_line</name>
         <load_address>0xbc4</load_address>
         <run_address>0xbc4</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x2332</load_address>
         <run_address>0x2332</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_line</name>
         <load_address>0x250e</load_address>
         <run_address>0x250e</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_line</name>
         <load_address>0x2a28</load_address>
         <run_address>0x2a28</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x2a66</load_address>
         <run_address>0x2a66</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x2b26</load_address>
         <run_address>0x2b26</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_line</name>
         <load_address>0x2cee</load_address>
         <run_address>0x2cee</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_line</name>
         <load_address>0x2d55</load_address>
         <run_address>0x2d55</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_line</name>
         <load_address>0x2d96</load_address>
         <run_address>0x2d96</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x2e3a</load_address>
         <run_address>0x2e3a</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x82</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_loc</name>
         <load_address>0x82</load_address>
         <run_address>0x82</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_loc</name>
         <load_address>0x95</load_address>
         <run_address>0x95</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_loc</name>
         <load_address>0x1abc</load_address>
         <run_address>0x1abc</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_loc</name>
         <load_address>0x1b94</load_address>
         <run_address>0x1b94</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_loc</name>
         <load_address>0x1fb8</load_address>
         <run_address>0x1fb8</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_loc</name>
         <load_address>0x2027</load_address>
         <run_address>0x2027</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_loc</name>
         <load_address>0x218e</load_address>
         <run_address>0x218e</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x4f8</size>
         <contents>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x5c8</load_address>
         <run_address>0x5c8</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-11e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x5b8</load_address>
         <run_address>0x5b8</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-ce"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-e7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-61"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-121"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-de" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-df" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e0" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e1" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e2" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e3" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e5" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-101" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb14</size>
         <contents>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-123"/>
         </contents>
      </logical_group>
      <logical_group id="lg-103" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7534</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-122"/>
         </contents>
      </logical_group>
      <logical_group id="lg-105" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x358</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-107" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4f78</size>
         <contents>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-d1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-109" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x768</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-b8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10b" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2eda</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-83"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10d" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x21b4</size>
         <contents>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-94"/>
         </contents>
      </logical_group>
      <logical_group id="lg-117" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-81"/>
         </contents>
      </logical_group>
      <logical_group id="lg-120" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-129" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5e8</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-12a" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x8</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-12b" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x5e8</used_space>
         <unused_space>0x1fa18</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x4f8</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x5b8</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x5c8</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x5e8</start_address>
               <size>0x1fa18</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x208</used_space>
         <unused_space>0x7df8</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-e3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-e5"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200008</start_address>
               <size>0x7df8</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x5c8</load_address>
            <load_size>0xd</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x8</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x5e0</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x5e8</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x5e8</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x5d8</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x5e0</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3f">
         <name>GROUP1_IRQHandler</name>
         <value>0x2dd</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-40">
         <name>pwm_direction</name>
         <value>0x20200000</value>
         <object_component_ref idref="oc-61"/>
      </symbol>
      <symbol id="sm-41">
         <name>pwm_duty_cycle</name>
         <value>0x20200004</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-42">
         <name>main</name>
         <value>0x4e1</value>
         <object_component_ref idref="oc-72"/>
      </symbol>
      <symbol id="sm-5c">
         <name>SYSCFG_DL_init</name>
         <value>0x555</value>
         <object_component_ref idref="oc-89"/>
      </symbol>
      <symbol id="sm-5d">
         <name>SYSCFG_DL_initPower</name>
         <value>0x485</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-5e">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x3c5</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-5f">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x409</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-60">
         <name>SYSCFG_DL_PWM_Wheel_init</name>
         <value>0x351</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-6b">
         <name>Default_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6c">
         <name>Reset_Handler</name>
         <value>0x5ab</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-6d">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-6e">
         <name>NMI_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6f">
         <name>HardFault_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-70">
         <name>SVC_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-71">
         <name>PendSV_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-72">
         <name>SysTick_Handler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-73">
         <name>GROUP0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>TIMG8_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-75">
         <name>UART3_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>ADC0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>ADC1_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>CANFD0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>DAC0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>SPI0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>SPI1_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>UART1_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>UART2_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>UART0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>TIMG0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>TIMG6_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>TIMA0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>TIMA1_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>TIMG7_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>TIMG12_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-85">
         <name>I2C0_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>I2C1_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>AES_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>RTC_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>DMA_IRQHandler</name>
         <value>0x25f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8b">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8c">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8d">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8e">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8f">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-90">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-91">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-92">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-9b">
         <name>DL_Common_delayCycles</name>
         <value>0x58d</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-b2">
         <name>DL_Timer_setClockConfig</name>
         <value>0x521</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-b3">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x57d</value>
         <object_component_ref idref="oc-5b"/>
      </symbol>
      <symbol id="sm-b4">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x505</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-b5">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x53d</value>
         <object_component_ref idref="oc-cc"/>
      </symbol>
      <symbol id="sm-b6">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-cb"/>
      </symbol>
      <symbol id="sm-c1">
         <name>_c_int00_noargs</name>
         <value>0x4b9</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-c2">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-ce">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x449</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-d6">
         <name>_system_pre_init</name>
         <value>0x5af</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-e1">
         <name>__TI_decompress_none</name>
         <value>0x569</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-ec">
         <name>__TI_decompress_lzss</name>
         <value>0x261</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-f6">
         <name>abort</name>
         <value>0x5a1</value>
         <object_component_ref idref="oc-8e"/>
      </symbol>
      <symbol id="sm-100">
         <name>HOSTexit</name>
         <value>0x5a7</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-101">
         <name>C$$EXIT</name>
         <value>0x5a6</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-107">
         <name>__aeabi_memcpy</name>
         <value>0x599</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-108">
         <name>__aeabi_memcpy4</name>
         <value>0x599</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-109">
         <name>__aeabi_memcpy8</name>
         <value>0x599</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-122">
         <name>memcpy</name>
         <value>0x1c5</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-123">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-126">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-127">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
