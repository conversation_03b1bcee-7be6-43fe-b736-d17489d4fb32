#include "ti_msp_dl_config.h"
/*------------------------------------------------------------------------------------------
小车循迹：灰度传感器gpio定义（PA26,PA24,PA22,PA15,PA17），
R1  ---  PA26
R2  ---  PA24
M   ---  PA22
L1  ---  PA15
L2  ---  PA17
循迹逻辑：（扫描到黑线输出低电平，扫描到白线输出高电平）黑线路径是1M*1M的正方形线宽刚好卡在L1和R1之间，我的灰度传感器排列顺序L2、L1、M、R1、R2，
1.L2、L1、R1、R2为高电平、M为低电平的时候，说明小车在黑线中间，此时小车应该直行，可以加速。
2.L2、R2、R1为高电平、L1、M为低电平的时候，说明小车稍稍偏右，此时小车应该左转矫正。
3.L2、L1、R2为高电平、R1、M为低电平的时候，说明小车稍稍偏左，此时小车应该右转矫正。
4.L2、L1低电平，M、R1、R2为高电平的时候，说明小车走到黑线的直角了，此时小车应该左转90度。
5.L2、L1、R1为高电平，R2、M为低电平的时候，说明小车走到黑线的直角了，此时小车应该右转90度。
我的小车是符合阿克曼转向模型的（第一类，阿克曼转向梯形（下图（a）），内轮转角＞外轮；理想的阿克曼梯形内外轮转角垂线相交于后轴延长线上），前两轮由一个舵机控制范围是（1400-8000），后两轮子需要差速符合阿克曼转向，差速需要你来计算，
前轴和后轴相距133mm，后轮轴距126mm，前轮轴距126mm，

小车电机控制命令（16进制）：

命令功能：电机使能控制
命令格式：地址 + 0xF3 + 0xAB + 使能状态 + 多机同步标志 + 校验字节
令示例：发送01 F3 AB 01 00 6B

命令功能：立即停止
命令格式：地址 + 0xFE + 0x98 + 多机同步标志 + 校验字节
命令示例：01 FE 98 00 6B
数据解析：让电机立即停止转动（紧急刹车），可用于速度模式和位置模式都可以使用该命令。

命令功能：速度模式控制
命令格式：地址 + 0xF6 + 方向 + 速度 + 加速度 + 多机同步标志 + 校验字节
命令示例：01 F6 01 05 DC 0A 00 6B
数据解析：01 表示旋转方向为 CCW（00 表示 CW），05 DC 表示速度为 0x05DC =1500(RPM)，0A表示加速度档位为0x0A =10，00表示不启用多机同步（01表示启用）

命令功能：位置模式控制
命令格式：地址 + 0xFD+ 方向 + 速度+ 加速度 + 脉冲数 + 相对/绝对模式标志 +多机同步标志 + 校验字节
命令示例：01 FD 01 05 DC 00 00 00 7D 00 00 00 6B
数据解析：01 表示旋转方向为 CCW（00 表示 CW），05 DC 表示速度为 0x05DC =1500(RPM)，00表示加速度档位为0x00 = 0，00 00 7D 00表示脉冲数为 0x00007D00 =
32000个，00表示相对位置模式（01表示绝对位置模式），00表示不启用多机同步（01表示启用）

命令功能：多机同步运动
命令格式：地址 + 0xFF + 0x66 + 校验字节
命令返回：地址 + 0xFF + 命令状态 + 校验字节
命令示例：发送01 FF 66 6B

二、同步控制：
比如有地址1、地址2 两个电机，假设需要：
1 地址电机速度1500RPM，加速度档位为8，相对运动-3600.0°； 
2 地址电机速度1000RPM，加速度档位为10，绝对运动7200.0°； 
1 和2地址电机要同步开始运动（同时开始运动）；
则，可以按照以下操作进行两者的同步控制：
1. 先发送1 地址电机命令01 FD 01 05 DC 08 00 00 7D 00 00 01 6B，正确返回01 FD 02 6B，此时1地址电机先不会运动，因为多机同步标志为01；
2. 再发送2 地址电机命令02 FD 00 03 E8 0A 00 00 FA 00 01 01 6B，正确返回02 FD 02 6B，此时2地址电机先不会运动，因为多机同步标志为01；
3. 再发送多机同步运动命令00 FF 66 6B，此时 1和2地址电机开始同步运动；



------------------------------------------------------------------------------------------*/

// 全局变量用于存储PWM占空比值
volatile int pwm_duty_cycle = 1400;
// 全局变量用于控制PWM变化方向（1为增加，0为减少）
volatile int pwm_direction = 1;

// 中断服务函数 - 必须放在main函数外部
void GROUP1_IRQHandler(void)
{
    // 读取Group1的中断寄存器并清除中断标志位
    switch(DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1))
    {
        // 检查是否是KEY的GPIOB端口中断
        case KEY_INT_IIDX:
            // 如果按键按下PWM占空比变化500
            if(DL_GPIO_readPins(KEY_PORT, KEY_PIN_21_PIN) > 0)
            {
                // 根据方向增加或减少PWM占空比
                if(pwm_direction == 1)
                {
                    // 增加模式：每次增加500
                    pwm_duty_cycle += 500;
                    
                    // 如果达到8000，切换到减少模式
                    if(pwm_duty_cycle >= 8000)
                    {
                        pwm_duty_cycle = 8000;
                        pwm_direction = 0; // 切换到减少模式
                    }
                }
                else
                {
                    // 减少模式：每次减少500
                    pwm_duty_cycle -= 500;
                    
                    // 如果达到1400，切换到增加模式
                    if(pwm_duty_cycle <= 1400)
                    {
                        pwm_duty_cycle = 1400;
                        pwm_direction = 1; // 切换到增加模式
                    }
                }
                
                // 设置PWM占空比
                DL_TimerG_setCaptureCompareValue(PWM_Wheel_INST, pwm_duty_cycle, DL_TIMER_CC_1_INDEX);
            }
            break;
    }
}

int main(void)
{
    SYSCFG_DL_init();
    
    // 开启按键引脚的GPIOA端口中断
    NVIC_EnableIRQ(KEY_INT_IRQN);
    
    // 初始化PWM占空比
    DL_TimerG_setCaptureCompareValue(PWM_Wheel_INST, pwm_duty_cycle, DL_TIMER_CC_1_INDEX);
    
    while (1)
    {
        // 主循环，可以添加其他功能
        // 当前PWM占空比通过按键中断来调节
    }
}
#include "ti_msp_dl_config.h"

volatile unsigned char uart_data = 0;

void uart0_send_char(char ch); //串口0发送单个字符
void uart0_send_string(char* str); //串口0发送字符串

int main(void)
{
    SYSCFG_DL_init();
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    //使能串口中断
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);

    uart0_send_string("uart0 start work\r\n");
    while (1)
    {

    }
}

//串口发送单个字符
void uart0_send_char(char ch)
{
    //当串口0忙的时候等待，不忙的时候再发送传进来的字符
    while( DL_UART_isBusy(UART_0_INST) == true );
    //发送单个字符
    DL_UART_Main_transmitData(UART_0_INST, ch);
}
//串口发送字符串
void uart0_send_string(char* str)
{
    //当前字符串地址不在结尾 并且 字符串首地址不为空
    while(*str!=0&&str!=0)
    {
        //发送字符串首地址中的字符，并且在发送完成之后首地址自增
        uart0_send_char(*str++);
    }
}

//串口的中断服务函数
void UART_0_INST_IRQHandler(void)
{
    //如果产生了串口中断
    switch( DL_UART_getPendingInterrupt(UART_0_INST) )
    {
        case DL_UART_IIDX_RX://如果是接收中断
            //将发送过来的数据保存在变量中
            uart_data = DL_UART_Main_receiveData(UART_0_INST);
            //将保存的数据再发送出去
            uart0_send_char(uart_data);
            break;

        default://其他的串口中断
            break;
    }
}