#include "ti_msp_dl_config.h"

// 全局变量用于存储PWM占空比值
volatile int pwm_duty_cycle = 1400;
// 全局变量用于控制PWM变化方向（1为增加，0为减少）
volatile int pwm_direction = 1;

// 中断服务函数 - 必须放在main函数外部
void GROUP1_IRQHandler(void)
{
    // 读取Group1的中断寄存器并清除中断标志位
    switch(DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1))
    {
        // 检查是否是KEY的GPIOB端口中断
        case KEY_INT_IIDX:
            // 如果按键按下PWM占空比变化500
            if(DL_GPIO_readPins(KEY_PORT, KEY_PIN_21_PIN) > 0)
            {
                // 根据方向增加或减少PWM占空比
                if(pwm_direction == 1)
                {
                    // 增加模式：每次增加500
                    pwm_duty_cycle += 500;
                    
                    // 如果达到8000，切换到减少模式
                    if(pwm_duty_cycle >= 8000)
                    {
                        pwm_duty_cycle = 8000;
                        pwm_direction = 0; // 切换到减少模式
                    }
                }
                else
                {
                    // 减少模式：每次减少500
                    pwm_duty_cycle -= 500;
                    
                    // 如果达到1400，切换到增加模式
                    if(pwm_duty_cycle <= 1400)
                    {
                        pwm_duty_cycle = 1400;
                        pwm_direction = 1; // 切换到增加模式
                    }
                }
                
                // 设置PWM占空比
                DL_TimerG_setCaptureCompareValue(PWM_Wheel_INST, pwm_duty_cycle, DL_TIMER_CC_1_INDEX);
            }
            break;
    }
}

int main(void)
{
    SYSCFG_DL_init();
    
    // 开启按键引脚的GPIOA端口中断
    NVIC_EnableIRQ(KEY_INT_IRQN);
    
    // 初始化PWM占空比
    DL_TimerG_setCaptureCompareValue(PWM_Wheel_INST, pwm_duty_cycle, DL_TIMER_CC_1_INDEX);
    
    while (1)
    {
        // 主循环，可以添加其他功能
        // 当前PWM占空比通过按键中断来调节
    }
}