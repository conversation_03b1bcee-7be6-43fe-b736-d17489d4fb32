################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/overCurrentConfig.c \
../ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceCurrentEstimConfig.c \
../ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceCurrentLimitConfig.c \
../ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceLimitsConfig.c \
../ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceVoltageLimitConfig.c 

C_DEPS += \
./ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/overCurrentConfig.d \
./ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceCurrentEstimConfig.d \
./ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceCurrentLimitConfig.d \
./ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceLimitsConfig.d \
./ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceVoltageLimitConfig.d 

OBJS += \
./ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/overCurrentConfig.o \
./ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceCurrentEstimConfig.o \
./ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceCurrentLimitConfig.o \
./ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceLimitsConfig.o \
./ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceVoltageLimitConfig.o 

OBJS__QUOTED += \
"ti\motor_control_universal_foc\modules\algoLib\modulesInterconnect\algoConfig\sourceLimitsConfig\source\overCurrentConfig.o" \
"ti\motor_control_universal_foc\modules\algoLib\modulesInterconnect\algoConfig\sourceLimitsConfig\source\sourceCurrentEstimConfig.o" \
"ti\motor_control_universal_foc\modules\algoLib\modulesInterconnect\algoConfig\sourceLimitsConfig\source\sourceCurrentLimitConfig.o" \
"ti\motor_control_universal_foc\modules\algoLib\modulesInterconnect\algoConfig\sourceLimitsConfig\source\sourceLimitsConfig.o" \
"ti\motor_control_universal_foc\modules\algoLib\modulesInterconnect\algoConfig\sourceLimitsConfig\source\sourceVoltageLimitConfig.o" 

C_DEPS__QUOTED += \
"ti\motor_control_universal_foc\modules\algoLib\modulesInterconnect\algoConfig\sourceLimitsConfig\source\overCurrentConfig.d" \
"ti\motor_control_universal_foc\modules\algoLib\modulesInterconnect\algoConfig\sourceLimitsConfig\source\sourceCurrentEstimConfig.d" \
"ti\motor_control_universal_foc\modules\algoLib\modulesInterconnect\algoConfig\sourceLimitsConfig\source\sourceCurrentLimitConfig.d" \
"ti\motor_control_universal_foc\modules\algoLib\modulesInterconnect\algoConfig\sourceLimitsConfig\source\sourceLimitsConfig.d" \
"ti\motor_control_universal_foc\modules\algoLib\modulesInterconnect\algoConfig\sourceLimitsConfig\source\sourceVoltageLimitConfig.d" 

C_SRCS__QUOTED += \
"../ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/overCurrentConfig.c" \
"../ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceCurrentEstimConfig.c" \
"../ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceCurrentLimitConfig.c" \
"../ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceLimitsConfig.c" \
"../ti/motor_control_universal_foc/modules/algoLib/modulesInterconnect/algoConfig/sourceLimitsConfig/source/sourceVoltageLimitConfig.c" 


