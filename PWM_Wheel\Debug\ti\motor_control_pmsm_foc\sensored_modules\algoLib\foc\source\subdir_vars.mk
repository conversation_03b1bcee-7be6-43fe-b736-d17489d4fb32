################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/abnormalSpeedStallDetect.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/closeLoop.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/fluxWeak.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/foc.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/hallCalib.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/hallEstimator.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/idReferencing.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/mtpa.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/noMotorStallDetect.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/openLoop.c \
../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/stallDetect.c 

C_DEPS += \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/abnormalSpeedStallDetect.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/closeLoop.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/fluxWeak.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/foc.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/hallCalib.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/hallEstimator.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/idReferencing.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/mtpa.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/noMotorStallDetect.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/openLoop.d \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/stallDetect.d 

OBJS += \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/abnormalSpeedStallDetect.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/closeLoop.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/fluxWeak.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/foc.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/hallCalib.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/hallEstimator.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/idReferencing.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/mtpa.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/noMotorStallDetect.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/openLoop.o \
./ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/stallDetect.o 

OBJS__QUOTED += \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\abnormalSpeedStallDetect.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\closeLoop.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\fluxWeak.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\foc.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\hallCalib.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\hallEstimator.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\idReferencing.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\mtpa.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\noMotorStallDetect.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\openLoop.o" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\stallDetect.o" 

C_DEPS__QUOTED += \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\abnormalSpeedStallDetect.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\closeLoop.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\fluxWeak.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\foc.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\hallCalib.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\hallEstimator.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\idReferencing.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\mtpa.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\noMotorStallDetect.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\openLoop.d" \
"ti\motor_control_pmsm_foc\sensored_modules\algoLib\foc\source\stallDetect.d" 

C_SRCS__QUOTED += \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/abnormalSpeedStallDetect.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/closeLoop.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/fluxWeak.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/foc.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/hallCalib.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/hallEstimator.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/idReferencing.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/mtpa.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/noMotorStallDetect.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/openLoop.c" \
"../ti/motor_control_pmsm_foc/sensored_modules/algoLib/foc/source/stallDetect.c" 


