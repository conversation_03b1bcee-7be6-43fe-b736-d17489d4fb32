******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 02:23:57 2025

OUTPUT FILE NAME:   <PWM_Wheel.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000005ed


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000738  0001f8c8  R  X
  SRAM                  20200000   00008000  00000208  00007df8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000738   00000738    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000640   00000640    r-x .text
  00000700    00000700    00000018   00000018    r-- .rodata
  00000718    00000718    00000020   00000020    r-- .cinit
20200000    20200000    00000008   00000000    rw-
  20200000    20200000    00000008   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000640     
                  000000c0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000001c4    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000025e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000260    00000094     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000002f4    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000384    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000400    00000074     empty.o (.text.GROUP1_IRQHandler)
                  00000474    00000074     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_Wheel_init)
                  000004e8    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00000530    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000570    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000005b0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000005ec    00000028            : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000614    00000024     empty.o (.text.main)
                  00000638    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00000654    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00000670    00000018                 : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000688    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000006a0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000006b2    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000006c4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000006d4    0000000a                 : dl_common.o (.text.DL_Common_delayCycles)
                  000006de    00000002     --HOLE-- [fill = 0]
                  000006e0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000006e8    00000006     libc.a : exit.c.obj (.text:abort)
                  000006ee    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000006f2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000006f6    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000006fa    00000006     --HOLE-- [fill = 0]

.cinit     0    00000718    00000020     
                  00000718    0000000d     (.cinit..data.load) [load image, compression = lzss]
                  00000725    00000003     --HOLE-- [fill = 0]
                  00000728    00000008     (__TI_handler_table)
                  00000730    00000008     (__TI_cinit_table)

.rodata    0    00000700    00000018     
                  00000700    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000070a    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  0000070c    00000008     ti_msp_dl_config.o (.rodata.gPWM_WheelConfig)
                  00000714    00000003     ti_msp_dl_config.o (.rodata.gPWM_WheelClockConfig)
                  00000717    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000008     UNINITIALIZED
                  20200000    00000004     empty.o (.data.pwm_direction)
                  20200004    00000004     empty.o (.data.pwm_duty_cycle)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             560    23        0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       empty.o                        152    0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         718    215       8      
                                                              
    D:/App_anzhuangbao/TI/CCS_20.2.0.00012/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_uart.o                      90     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         456    0         0      
                                                              
    D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         406    0         0      
                                                              
    D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      29        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   1592   244       520    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000730 records: 1, size/record: 8, table size: 8
	.data: load addr=00000718, load size=0000000d bytes, run addr=20200000, run size=00000008 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000728 records: 2, size/record: 4, table size: 8
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
0000025f  ADC0_IRQHandler                 
0000025f  ADC1_IRQHandler                 
0000025f  AES_IRQHandler                  
000006ee  C$$EXIT                         
0000025f  CANFD0_IRQHandler               
0000025f  DAC0_IRQHandler                 
000006d5  DL_Common_delayCycles           
000000c1  DL_Timer_initFourCCPWMMode      
00000639  DL_Timer_setCaptCompUpdateMethod
00000671  DL_Timer_setCaptureCompareOutCtl
000006c5  DL_Timer_setCaptureCompareValue 
00000655  DL_Timer_setClockConfig         
000004e9  DL_UART_init                    
000006a1  DL_UART_setClockConfig          
0000025f  DMA_IRQHandler                  
0000025f  Default_Handler                 
0000025f  GROUP0_IRQHandler               
00000401  GROUP1_IRQHandler               
000006ef  HOSTexit                        
0000025f  HardFault_Handler               
0000025f  I2C0_IRQHandler                 
0000025f  I2C1_IRQHandler                 
0000025f  NMI_Handler                     
0000025f  PendSV_Handler                  
0000025f  RTC_IRQHandler                  
000006f3  Reset_Handler                   
0000025f  SPI0_IRQHandler                 
0000025f  SPI1_IRQHandler                 
0000025f  SVC_Handler                     
000002f5  SYSCFG_DL_GPIO_init             
00000475  SYSCFG_DL_PWM_Wheel_init        
00000531  SYSCFG_DL_SYSCTL_init           
00000261  SYSCFG_DL_UART_0_init           
00000689  SYSCFG_DL_init                  
00000571  SYSCFG_DL_initPower             
0000025f  SysTick_Handler                 
0000025f  TIMA0_IRQHandler                
0000025f  TIMA1_IRQHandler                
0000025f  TIMG0_IRQHandler                
0000025f  TIMG12_IRQHandler               
0000025f  TIMG6_IRQHandler                
0000025f  TIMG7_IRQHandler                
0000025f  TIMG8_IRQHandler                
0000025f  UART0_IRQHandler                
0000025f  UART1_IRQHandler                
0000025f  UART2_IRQHandler                
0000025f  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000730  __TI_CINIT_Base                 
00000738  __TI_CINIT_Limit                
00000738  __TI_CINIT_Warm                 
00000728  __TI_Handler_Table_Base         
00000730  __TI_Handler_Table_Limit        
000005b1  __TI_auto_init_nobinit_nopinit  
00000385  __TI_decompress_lzss            
000006b3  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
000006e1  __aeabi_memcpy                  
000006e1  __aeabi_memcpy4                 
000006e1  __aeabi_memcpy8                 
ffffffff  __binit__                       
UNDEFED   __mpu_init                      
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000005ed  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000006f7  _system_pre_init                
000006e9  abort                           
ffffffff  binit                           
00000000  interruptVectors                
00000615  main                            
000001c5  memcpy                          
20200000  pwm_direction                   
20200004  pwm_duty_cycle                  


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  DL_Timer_initFourCCPWMMode      
000001c5  memcpy                          
00000200  __STACK_SIZE                    
0000025f  ADC0_IRQHandler                 
0000025f  ADC1_IRQHandler                 
0000025f  AES_IRQHandler                  
0000025f  CANFD0_IRQHandler               
0000025f  DAC0_IRQHandler                 
0000025f  DMA_IRQHandler                  
0000025f  Default_Handler                 
0000025f  GROUP0_IRQHandler               
0000025f  HardFault_Handler               
0000025f  I2C0_IRQHandler                 
0000025f  I2C1_IRQHandler                 
0000025f  NMI_Handler                     
0000025f  PendSV_Handler                  
0000025f  RTC_IRQHandler                  
0000025f  SPI0_IRQHandler                 
0000025f  SPI1_IRQHandler                 
0000025f  SVC_Handler                     
0000025f  SysTick_Handler                 
0000025f  TIMA0_IRQHandler                
0000025f  TIMA1_IRQHandler                
0000025f  TIMG0_IRQHandler                
0000025f  TIMG12_IRQHandler               
0000025f  TIMG6_IRQHandler                
0000025f  TIMG7_IRQHandler                
0000025f  TIMG8_IRQHandler                
0000025f  UART0_IRQHandler                
0000025f  UART1_IRQHandler                
0000025f  UART2_IRQHandler                
0000025f  UART3_IRQHandler                
00000261  SYSCFG_DL_UART_0_init           
000002f5  SYSCFG_DL_GPIO_init             
00000385  __TI_decompress_lzss            
00000401  GROUP1_IRQHandler               
00000475  SYSCFG_DL_PWM_Wheel_init        
000004e9  DL_UART_init                    
00000531  SYSCFG_DL_SYSCTL_init           
00000571  SYSCFG_DL_initPower             
000005b1  __TI_auto_init_nobinit_nopinit  
000005ed  _c_int00_noargs                 
00000615  main                            
00000639  DL_Timer_setCaptCompUpdateMethod
00000655  DL_Timer_setClockConfig         
00000671  DL_Timer_setCaptureCompareOutCtl
00000689  SYSCFG_DL_init                  
000006a1  DL_UART_setClockConfig          
000006b3  __TI_decompress_none            
000006c5  DL_Timer_setCaptureCompareValue 
000006d5  DL_Common_delayCycles           
000006e1  __aeabi_memcpy                  
000006e1  __aeabi_memcpy4                 
000006e1  __aeabi_memcpy8                 
000006e9  abort                           
000006ee  C$$EXIT                         
000006ef  HOSTexit                        
000006f3  Reset_Handler                   
000006f7  _system_pre_init                
00000728  __TI_Handler_Table_Base         
00000730  __TI_CINIT_Base                 
00000730  __TI_Handler_Table_Limit        
00000738  __TI_CINIT_Limit                
00000738  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  pwm_direction                   
20200004  pwm_duty_cycle                  
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[90 symbols]
