******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 23:26:23 2025

OUTPUT FILE NAME:   <PWM_Wheel.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000004b9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000005e8  0001fa18  R  X
  SRAM                  20200000   00008000  00000208  00007df8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000005e8   000005e8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000004f8   000004f8    r-x .text
  000005b8    000005b8    00000010   00000010    r-- .rodata
  000005c8    000005c8    00000020   00000020    r-- .cinit
20200000    20200000    00000008   00000000    rw-
  20200000    20200000    00000008   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000004f8     
                  000000c0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000001c4    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000025e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000260    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000002dc    00000074     empty.o (.text.GROUP1_IRQHandler)
                  00000350    00000074     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_Wheel_init)
                  000003c4    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000408    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000448    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000484    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000004b8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000004e0    00000024     empty.o (.text.main)
                  00000504    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00000520    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000053c    00000018                 : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000554    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000568    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  0000057a    00000002     --HOLE-- [fill = 0]
                  0000057c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000058c    0000000a                 : dl_common.o (.text.DL_Common_delayCycles)
                  00000596    00000002     --HOLE-- [fill = 0]
                  00000598    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000005a0    00000006     libc.a : exit.c.obj (.text:abort)
                  000005a6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000005aa    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000005ae    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000005b2    00000006     --HOLE-- [fill = 0]

.cinit     0    000005c8    00000020     
                  000005c8    0000000d     (.cinit..data.load) [load image, compression = lzss]
                  000005d5    00000003     --HOLE-- [fill = 0]
                  000005d8    00000008     (__TI_handler_table)
                  000005e0    00000008     (__TI_cinit_table)

.rodata    0    000005b8    00000010     
                  000005b8    00000008     ti_msp_dl_config.o (.rodata.gPWM_WheelConfig)
                  000005c0    00000003     ti_msp_dl_config.o (.rodata.gPWM_WheelClockConfig)
                  000005c3    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000008     UNINITIALIZED
                  20200000    00000004     empty.o (.data.pwm_direction)
                  20200004    00000004     empty.o (.data.pwm_duty_cycle)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             320    11        0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       empty.o                        152    0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         478    203       8      
                                                              
    D:/App_anzhuangbao/TI/CCS_20.2.0.00012/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         366    0         0      
                                                              
    D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         406    0         0      
                                                              
    D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\App_anzhuangbao\TI\CCS_20.2.0.00012\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      29        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   1262   232       520    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000005e0 records: 1, size/record: 8, table size: 8
	.data: load addr=000005c8, load size=0000000d bytes, run addr=20200000, run size=00000008 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000005d8 records: 2, size/record: 4, table size: 8
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
0000025f  ADC0_IRQHandler                 
0000025f  ADC1_IRQHandler                 
0000025f  AES_IRQHandler                  
000005a6  C$$EXIT                         
0000025f  CANFD0_IRQHandler               
0000025f  DAC0_IRQHandler                 
0000058d  DL_Common_delayCycles           
000000c1  DL_Timer_initFourCCPWMMode      
00000505  DL_Timer_setCaptCompUpdateMethod
0000053d  DL_Timer_setCaptureCompareOutCtl
0000057d  DL_Timer_setCaptureCompareValue 
00000521  DL_Timer_setClockConfig         
0000025f  DMA_IRQHandler                  
0000025f  Default_Handler                 
0000025f  GROUP0_IRQHandler               
000002dd  GROUP1_IRQHandler               
000005a7  HOSTexit                        
0000025f  HardFault_Handler               
0000025f  I2C0_IRQHandler                 
0000025f  I2C1_IRQHandler                 
0000025f  NMI_Handler                     
0000025f  PendSV_Handler                  
0000025f  RTC_IRQHandler                  
000005ab  Reset_Handler                   
0000025f  SPI0_IRQHandler                 
0000025f  SPI1_IRQHandler                 
0000025f  SVC_Handler                     
000003c5  SYSCFG_DL_GPIO_init             
00000351  SYSCFG_DL_PWM_Wheel_init        
00000409  SYSCFG_DL_SYSCTL_init           
00000555  SYSCFG_DL_init                  
00000485  SYSCFG_DL_initPower             
0000025f  SysTick_Handler                 
0000025f  TIMA0_IRQHandler                
0000025f  TIMA1_IRQHandler                
0000025f  TIMG0_IRQHandler                
0000025f  TIMG12_IRQHandler               
0000025f  TIMG6_IRQHandler                
0000025f  TIMG7_IRQHandler                
0000025f  TIMG8_IRQHandler                
0000025f  UART0_IRQHandler                
0000025f  UART1_IRQHandler                
0000025f  UART2_IRQHandler                
0000025f  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
000005e0  __TI_CINIT_Base                 
000005e8  __TI_CINIT_Limit                
000005e8  __TI_CINIT_Warm                 
000005d8  __TI_Handler_Table_Base         
000005e0  __TI_Handler_Table_Limit        
00000449  __TI_auto_init_nobinit_nopinit  
00000261  __TI_decompress_lzss            
00000569  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00000599  __aeabi_memcpy                  
00000599  __aeabi_memcpy4                 
00000599  __aeabi_memcpy8                 
ffffffff  __binit__                       
UNDEFED   __mpu_init                      
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000004b9  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000005af  _system_pre_init                
000005a1  abort                           
ffffffff  binit                           
00000000  interruptVectors                
000004e1  main                            
000001c5  memcpy                          
20200000  pwm_direction                   
20200004  pwm_duty_cycle                  


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  DL_Timer_initFourCCPWMMode      
000001c5  memcpy                          
00000200  __STACK_SIZE                    
0000025f  ADC0_IRQHandler                 
0000025f  ADC1_IRQHandler                 
0000025f  AES_IRQHandler                  
0000025f  CANFD0_IRQHandler               
0000025f  DAC0_IRQHandler                 
0000025f  DMA_IRQHandler                  
0000025f  Default_Handler                 
0000025f  GROUP0_IRQHandler               
0000025f  HardFault_Handler               
0000025f  I2C0_IRQHandler                 
0000025f  I2C1_IRQHandler                 
0000025f  NMI_Handler                     
0000025f  PendSV_Handler                  
0000025f  RTC_IRQHandler                  
0000025f  SPI0_IRQHandler                 
0000025f  SPI1_IRQHandler                 
0000025f  SVC_Handler                     
0000025f  SysTick_Handler                 
0000025f  TIMA0_IRQHandler                
0000025f  TIMA1_IRQHandler                
0000025f  TIMG0_IRQHandler                
0000025f  TIMG12_IRQHandler               
0000025f  TIMG6_IRQHandler                
0000025f  TIMG7_IRQHandler                
0000025f  TIMG8_IRQHandler                
0000025f  UART0_IRQHandler                
0000025f  UART1_IRQHandler                
0000025f  UART2_IRQHandler                
0000025f  UART3_IRQHandler                
00000261  __TI_decompress_lzss            
000002dd  GROUP1_IRQHandler               
00000351  SYSCFG_DL_PWM_Wheel_init        
000003c5  SYSCFG_DL_GPIO_init             
00000409  SYSCFG_DL_SYSCTL_init           
00000449  __TI_auto_init_nobinit_nopinit  
00000485  SYSCFG_DL_initPower             
000004b9  _c_int00_noargs                 
000004e1  main                            
00000505  DL_Timer_setCaptCompUpdateMethod
00000521  DL_Timer_setClockConfig         
0000053d  DL_Timer_setCaptureCompareOutCtl
00000555  SYSCFG_DL_init                  
00000569  __TI_decompress_none            
0000057d  DL_Timer_setCaptureCompareValue 
0000058d  DL_Common_delayCycles           
00000599  __aeabi_memcpy                  
00000599  __aeabi_memcpy4                 
00000599  __aeabi_memcpy8                 
000005a1  abort                           
000005a6  C$$EXIT                         
000005a7  HOSTexit                        
000005ab  Reset_Handler                   
000005af  _system_pre_init                
000005d8  __TI_Handler_Table_Base         
000005e0  __TI_CINIT_Base                 
000005e0  __TI_Handler_Table_Limit        
000005e8  __TI_CINIT_Limit                
000005e8  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  pwm_direction                   
20200004  pwm_duty_cycle                  
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[87 symbols]
