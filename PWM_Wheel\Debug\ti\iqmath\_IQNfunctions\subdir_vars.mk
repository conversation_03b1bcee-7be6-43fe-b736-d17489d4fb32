################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../ti/iqmath/_IQNfunctions/_IQNasin_acos.c \
../ti/iqmath/_IQNfunctions/_IQNatan2.c \
../ti/iqmath/_IQNfunctions/_IQNdiv.c \
../ti/iqmath/_IQNfunctions/_IQNexp.c \
../ti/iqmath/_IQNfunctions/_IQNfrac.c \
../ti/iqmath/_IQNfunctions/_IQNlog.c \
../ti/iqmath/_IQNfunctions/_IQNmpy.c \
../ti/iqmath/_IQNfunctions/_IQNmpyIQX.c \
../ti/iqmath/_IQNfunctions/_IQNrepeat.c \
../ti/iqmath/_IQNfunctions/_IQNrmpy.c \
../ti/iqmath/_IQNfunctions/_IQNrsmpy.c \
../ti/iqmath/_IQNfunctions/_IQNsin_cos.c \
../ti/iqmath/_IQNfunctions/_IQNsqrt.c \
../ti/iqmath/_IQNfunctions/_IQNtables.c \
../ti/iqmath/_IQNfunctions/_IQNtoF.c \
../ti/iqmath/_IQNfunctions/_IQNtoa.c \
../ti/iqmath/_IQNfunctions/_IQNversion.c \
../ti/iqmath/_IQNfunctions/_atoIQN.c 

C_DEPS += \
./ti/iqmath/_IQNfunctions/_IQNasin_acos.d \
./ti/iqmath/_IQNfunctions/_IQNatan2.d \
./ti/iqmath/_IQNfunctions/_IQNdiv.d \
./ti/iqmath/_IQNfunctions/_IQNexp.d \
./ti/iqmath/_IQNfunctions/_IQNfrac.d \
./ti/iqmath/_IQNfunctions/_IQNlog.d \
./ti/iqmath/_IQNfunctions/_IQNmpy.d \
./ti/iqmath/_IQNfunctions/_IQNmpyIQX.d \
./ti/iqmath/_IQNfunctions/_IQNrepeat.d \
./ti/iqmath/_IQNfunctions/_IQNrmpy.d \
./ti/iqmath/_IQNfunctions/_IQNrsmpy.d \
./ti/iqmath/_IQNfunctions/_IQNsin_cos.d \
./ti/iqmath/_IQNfunctions/_IQNsqrt.d \
./ti/iqmath/_IQNfunctions/_IQNtables.d \
./ti/iqmath/_IQNfunctions/_IQNtoF.d \
./ti/iqmath/_IQNfunctions/_IQNtoa.d \
./ti/iqmath/_IQNfunctions/_IQNversion.d \
./ti/iqmath/_IQNfunctions/_atoIQN.d 

OBJS += \
./ti/iqmath/_IQNfunctions/_IQNasin_acos.o \
./ti/iqmath/_IQNfunctions/_IQNatan2.o \
./ti/iqmath/_IQNfunctions/_IQNdiv.o \
./ti/iqmath/_IQNfunctions/_IQNexp.o \
./ti/iqmath/_IQNfunctions/_IQNfrac.o \
./ti/iqmath/_IQNfunctions/_IQNlog.o \
./ti/iqmath/_IQNfunctions/_IQNmpy.o \
./ti/iqmath/_IQNfunctions/_IQNmpyIQX.o \
./ti/iqmath/_IQNfunctions/_IQNrepeat.o \
./ti/iqmath/_IQNfunctions/_IQNrmpy.o \
./ti/iqmath/_IQNfunctions/_IQNrsmpy.o \
./ti/iqmath/_IQNfunctions/_IQNsin_cos.o \
./ti/iqmath/_IQNfunctions/_IQNsqrt.o \
./ti/iqmath/_IQNfunctions/_IQNtables.o \
./ti/iqmath/_IQNfunctions/_IQNtoF.o \
./ti/iqmath/_IQNfunctions/_IQNtoa.o \
./ti/iqmath/_IQNfunctions/_IQNversion.o \
./ti/iqmath/_IQNfunctions/_atoIQN.o 

OBJS__QUOTED += \
"ti\iqmath\_IQNfunctions\_IQNasin_acos.o" \
"ti\iqmath\_IQNfunctions\_IQNatan2.o" \
"ti\iqmath\_IQNfunctions\_IQNdiv.o" \
"ti\iqmath\_IQNfunctions\_IQNexp.o" \
"ti\iqmath\_IQNfunctions\_IQNfrac.o" \
"ti\iqmath\_IQNfunctions\_IQNlog.o" \
"ti\iqmath\_IQNfunctions\_IQNmpy.o" \
"ti\iqmath\_IQNfunctions\_IQNmpyIQX.o" \
"ti\iqmath\_IQNfunctions\_IQNrepeat.o" \
"ti\iqmath\_IQNfunctions\_IQNrmpy.o" \
"ti\iqmath\_IQNfunctions\_IQNrsmpy.o" \
"ti\iqmath\_IQNfunctions\_IQNsin_cos.o" \
"ti\iqmath\_IQNfunctions\_IQNsqrt.o" \
"ti\iqmath\_IQNfunctions\_IQNtables.o" \
"ti\iqmath\_IQNfunctions\_IQNtoF.o" \
"ti\iqmath\_IQNfunctions\_IQNtoa.o" \
"ti\iqmath\_IQNfunctions\_IQNversion.o" \
"ti\iqmath\_IQNfunctions\_atoIQN.o" 

C_DEPS__QUOTED += \
"ti\iqmath\_IQNfunctions\_IQNasin_acos.d" \
"ti\iqmath\_IQNfunctions\_IQNatan2.d" \
"ti\iqmath\_IQNfunctions\_IQNdiv.d" \
"ti\iqmath\_IQNfunctions\_IQNexp.d" \
"ti\iqmath\_IQNfunctions\_IQNfrac.d" \
"ti\iqmath\_IQNfunctions\_IQNlog.d" \
"ti\iqmath\_IQNfunctions\_IQNmpy.d" \
"ti\iqmath\_IQNfunctions\_IQNmpyIQX.d" \
"ti\iqmath\_IQNfunctions\_IQNrepeat.d" \
"ti\iqmath\_IQNfunctions\_IQNrmpy.d" \
"ti\iqmath\_IQNfunctions\_IQNrsmpy.d" \
"ti\iqmath\_IQNfunctions\_IQNsin_cos.d" \
"ti\iqmath\_IQNfunctions\_IQNsqrt.d" \
"ti\iqmath\_IQNfunctions\_IQNtables.d" \
"ti\iqmath\_IQNfunctions\_IQNtoF.d" \
"ti\iqmath\_IQNfunctions\_IQNtoa.d" \
"ti\iqmath\_IQNfunctions\_IQNversion.d" \
"ti\iqmath\_IQNfunctions\_atoIQN.d" 

C_SRCS__QUOTED += \
"../ti/iqmath/_IQNfunctions/_IQNasin_acos.c" \
"../ti/iqmath/_IQNfunctions/_IQNatan2.c" \
"../ti/iqmath/_IQNfunctions/_IQNdiv.c" \
"../ti/iqmath/_IQNfunctions/_IQNexp.c" \
"../ti/iqmath/_IQNfunctions/_IQNfrac.c" \
"../ti/iqmath/_IQNfunctions/_IQNlog.c" \
"../ti/iqmath/_IQNfunctions/_IQNmpy.c" \
"../ti/iqmath/_IQNfunctions/_IQNmpyIQX.c" \
"../ti/iqmath/_IQNfunctions/_IQNrepeat.c" \
"../ti/iqmath/_IQNfunctions/_IQNrmpy.c" \
"../ti/iqmath/_IQNfunctions/_IQNrsmpy.c" \
"../ti/iqmath/_IQNfunctions/_IQNsin_cos.c" \
"../ti/iqmath/_IQNfunctions/_IQNsqrt.c" \
"../ti/iqmath/_IQNfunctions/_IQNtables.c" \
"../ti/iqmath/_IQNfunctions/_IQNtoF.c" \
"../ti/iqmath/_IQNfunctions/_IQNtoa.c" \
"../ti/iqmath/_IQNfunctions/_IQNversion.c" \
"../ti/iqmath/_IQNfunctions/_atoIQN.c" 


