################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/%.o: ../ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/App_anzhuangbao/TI/CCS_20.2.0.00012/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"F:/TI/CCS_Project/PWM_Wheel" -I"F:/TI/CCS_Project/PWM_Wheel/Debug" -I"D:/App_anzhuangbao/TI/CCS_20.2.0.00012/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"D:/App_anzhuangbao/TI/CCS_20.2.0.00012/mspm0_sdk_2_05_01_00/source" -gdwarf-3 -MMD -MP -MF"ti/motor_control_universal_foc/modules/algoLib/sourceLimits/source/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


