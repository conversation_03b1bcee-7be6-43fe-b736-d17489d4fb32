/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const GPIO3  = GPIO.addInstance();
const GPIO4  = GPIO.addInstance();
const GPIO5  = GPIO.addInstance();
const GPIO6  = GPIO.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                              = "KEY";
GPIO1.port                               = "PORTB";
GPIO1.associatedPins[0].$name            = "PIN_21";
GPIO1.associatedPins[0].direction        = "INPUT";
GPIO1.associatedPins[0].internalResistor = "PULL_UP";
GPIO1.associatedPins[0].assignedPin      = "21";
GPIO1.associatedPins[0].interruptEn      = true;
GPIO1.associatedPins[0].polarity         = "RISE";
GPIO1.associatedPins[0].pin.$assign      = "PB21";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                              = "R1";
GPIO2.port                               = "PORTA";
GPIO2.associatedPins[0].$name            = "PIN_26";
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].assignedPin      = "26";
GPIO2.associatedPins[0].interruptEn      = true;
GPIO2.associatedPins[0].polarity         = "RISE";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].pin.$assign      = "PA26";

GPIO3.$name                              = "R2";
GPIO3.port                               = "PORTA";
GPIO3.associatedPins[0].$name            = "PIN_24";
GPIO3.associatedPins[0].direction        = "INPUT";
GPIO3.associatedPins[0].assignedPin      = "24";
GPIO3.associatedPins[0].interruptEn      = true;
GPIO3.associatedPins[0].polarity         = "RISE";
GPIO3.associatedPins[0].internalResistor = "PULL_UP";
GPIO3.associatedPins[0].pin.$assign      = "PA24";

GPIO4.$name                              = "M";
GPIO4.port                               = "PORTA";
GPIO4.associatedPins[0].$name            = "PIN_22";
GPIO4.associatedPins[0].direction        = "INPUT";
GPIO4.associatedPins[0].assignedPin      = "22";
GPIO4.associatedPins[0].internalResistor = "PULL_UP";
GPIO4.associatedPins[0].interruptEn      = true;
GPIO4.associatedPins[0].polarity         = "RISE";
GPIO4.associatedPins[0].assignedPort     = "PORTA";
GPIO4.associatedPins[0].pin.$assign      = "PA22";

GPIO5.$name                              = "L1";
GPIO5.port                               = "PORTA";
GPIO5.associatedPins[0].$name            = "PIN_15";
GPIO5.associatedPins[0].direction        = "INPUT";
GPIO5.associatedPins[0].assignedPin      = "15";
GPIO5.associatedPins[0].interruptEn      = true;
GPIO5.associatedPins[0].polarity         = "RISE";
GPIO5.associatedPins[0].internalResistor = "PULL_UP";
GPIO5.associatedPins[0].pin.$assign      = "PA15";

GPIO6.$name                              = "L2";
GPIO6.associatedPins[0].$name            = "PIN_17";
GPIO6.associatedPins[0].direction        = "INPUT";
GPIO6.associatedPins[0].assignedPort     = "PORTA";
GPIO6.associatedPins[0].assignedPin      = "17";
GPIO6.associatedPins[0].interruptEn      = true;
GPIO6.associatedPins[0].polarity         = "RISE";
GPIO6.associatedPins[0].internalResistor = "PULL_UP";
GPIO6.associatedPins[0].pin.$assign      = "PA17";

PWM1.timerStartTimer                    = true;
PWM1.ccIndex                            = [1];
PWM1.clockDivider                       = 8;
PWM1.timerCount                         = 80000;
PWM1.$name                              = "PWM_Wheel";
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.peripheral.$assign                 = "TIMG12";
PWM1.peripheral.ccp1Pin.$assign         = "PA25";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.invert               = true;

SYSCTL.forceDefaultClkConfig = true;

UART1.$name                    = "UART_0";
UART1.targetBaudRate           = 115200;
UART1.enableFIFO               = true;
UART1.rxFifoThreshold          = "DL_UART_RX_FIFO_LEVEL_ONE_ENTRY";
UART1.enableInternalLoopback   = true;
UART1.enableDMARX              = false;
UART1.enableDMATX              = false;
UART1.enabledInterrupts        = ["RX"];
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution          = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution = "PA20";
Board.peripheral.swdioPin.$suggestSolution = "PA19";
SYSCTL.peripheral.$suggestSolution         = "SYSCTL";
